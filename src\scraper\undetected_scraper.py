"""
Alternative scraper using undetected-chromedriver for maximum stealth
"""
import asyncio
import random
import time
from typing import Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import undetected_chromedriver as uc
from loguru import logger
from src.config import settings
from src.database.models import TicketData


class UndetectedMinecraftScraper:
    """Undetected Chrome-based scraper for maximum stealth"""
    
    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
    
    async def initialize(self):
        """Initialize undetected Chrome driver"""
        try:
            logger.info("Initializing undetected Chrome driver...")
            
            # Configure Chrome options for maximum stealth
            options = uc.ChromeOptions()
            
            if settings.headless_mode:
                options.add_argument('--headless=new')
            
            # Anti-detection arguments
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')
            options.add_argument('--no-first-run')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--metrics-recording-only')
            options.add_argument('--no-report-upload')
            
            # Set realistic window size
            options.add_argument(f'--window-size={random.randint(1366, 1920)},{random.randint(768, 1080)}')
            
            # Initialize undetected Chrome
            self.driver = uc.Chrome(options=options, version_main=None)
            self.wait = WebDriverWait(self.driver, 30)
            
            # Set realistic timeouts
            self.driver.implicitly_wait(10)
            self.driver.set_page_load_timeout(45)
            
            logger.info("Undetected Chrome driver initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize undetected Chrome driver: {e}")
            raise
    
    async def submit_ticket(self, ticket_data: TicketData) -> bool:
        """Submit ticket using undetected Chrome"""
        max_attempts = 3
        
        for attempt in range(max_attempts):
            try:
                logger.info(f"Starting undetected ticket submission for {ticket_data.ticket_id} (attempt {attempt + 1}/{max_attempts})")
                
                if not self.driver:
                    await self.initialize()
                
                # Navigate with stealth
                if not await self.navigate_to_form():
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(5)
                        continue
                    return False
                
                # Fill form
                if not await self.fill_form(ticket_data):
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(5)
                        continue
                    return False
                
                # Submit form
                if not await self.submit_form():
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(5)
                        continue
                    return False
                
                logger.info(f"Successfully submitted ticket {ticket_data.ticket_id} using undetected Chrome")
                return True
                
            except Exception as e:
                logger.error(f"Undetected submission attempt {attempt + 1} failed: {e}")
                if attempt < max_attempts - 1:
                    await self.reset_driver()
                    await asyncio.sleep(5)
                    continue
                else:
                    return False
        
        return False
    
    async def navigate_to_form(self) -> bool:
        """Navigate to the ticket form with realistic behavior"""
        try:
            logger.info("Navigating to Minecraft support form with undetected Chrome")
            
            # Step 1: Visit a common site first (realistic browsing)
            common_sites = ["https://www.google.com", "https://www.minecraft.net"]
            start_site = random.choice(common_sites)
            
            logger.info(f"Starting browsing session at: {start_site}")
            self.driver.get(start_site)
            await self.human_delay(2, 4)
            
            # Perform realistic interactions
            await self.simulate_human_behavior()
            
            # Step 2: Navigate to target URL
            target_urls = [
                "https://help.minecraft.net/hc/en-us/requests/new",
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=360003469452",
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=4416074743565"
            ]
            
            for url in target_urls:
                try:
                    logger.info(f"Attempting to navigate to: {url}")
                    self.driver.get(url)
                    await self.human_delay(3, 6)
                    
                    # Check if we successfully loaded a form page
                    if self.verify_form_page():
                        logger.info(f"Successfully reached form page: {url}")
                        return True
                        
                except Exception as e:
                    logger.warning(f"Failed to navigate to {url}: {e}")
                    continue
            
            logger.error("Failed to navigate to any form URL")
            return False
            
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return False
    
    def verify_form_page(self) -> bool:
        """Verify we're on a form page"""
        try:
            # Look for form elements
            form_indicators = [
                "//form",
                "//input[@type='email']",
                "//textarea",
                "//select"
            ]
            
            for indicator in form_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element:
                        logger.info(f"Found form indicator: {indicator}")
                        return True
                except:
                    continue
            
            # Check URL for form indicators
            current_url = self.driver.current_url
            if any(keyword in current_url.lower() for keyword in ["request", "form", "contact", "support"]):
                logger.info("URL indicates form page")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error verifying form page: {e}")
            return False
    
    async def fill_form(self, ticket_data: TicketData) -> bool:
        """Fill the support form with ticket data"""
        try:
            logger.info("Filling support form with undetected Chrome")
            
            # Wait for form to load
            await self.human_delay(2, 4)
            
            # Form field selectors
            field_selectors = {
                'email': [
                    "//input[@type='email']",
                    "//input[contains(@name, 'email')]",
                    "//input[contains(@id, 'email')]"
                ],
                'subject': [
                    "//input[contains(@name, 'subject')]",
                    "//input[contains(@id, 'subject')]",
                    "//input[contains(@placeholder, 'subject')]"
                ],
                'description': [
                    "//textarea",
                    "//textarea[contains(@name, 'description')]",
                    "//textarea[contains(@id, 'description')]"
                ]
            }
            
            # Fill email
            if await self.fill_field(field_selectors['email'], ticket_data.email, "email"):
                logger.info("Email field filled successfully")
            else:
                logger.warning("Could not fill email field")
            
            await self.human_delay(1, 3)
            
            # Fill subject
            if await self.fill_field(field_selectors['subject'], ticket_data.subject, "subject"):
                logger.info("Subject field filled successfully")
            else:
                logger.warning("Could not fill subject field")
            
            await self.human_delay(1, 3)
            
            # Fill description
            full_description = f"Issue Type: {ticket_data.ticket_type}\n\n"
            full_description += f"Description: {ticket_data.description}\n\n"
            if ticket_data.minecraft_username:
                full_description += f"Minecraft Username: {ticket_data.minecraft_username}\n\n"
            full_description += "This ticket contains all relevant information for support assistance."
            
            if await self.fill_field(field_selectors['description'], full_description, "description"):
                logger.info("Description field filled successfully")
            else:
                logger.warning("Could not fill description field")
            
            return True
            
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return False
    
    async def fill_field(self, selectors, value, field_name) -> bool:
        """Fill a specific field with human-like behavior"""
        try:
            for selector in selectors:
                try:
                    element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    
                    # Scroll to element
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    await self.human_delay(0.5, 1)
                    
                    # Click to focus
                    element.click()
                    await self.human_delay(0.3, 0.7)
                    
                    # Clear field
                    element.clear()
                    await self.human_delay(0.2, 0.5)
                    
                    # Type with human-like delays
                    await self.type_like_human(element, value)
                    
                    logger.info(f"Successfully filled {field_name} field")
                    return True
                    
                except Exception as e:
                    logger.debug(f"Selector {selector} failed for {field_name}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error filling {field_name} field: {e}")
            return False
    
    async def type_like_human(self, element, text):
        """Type text with realistic human patterns"""
        try:
            for char in text:
                element.send_keys(char)
                # Random delay between characters
                await asyncio.sleep(random.uniform(0.05, 0.15))
                
                # Random pause occasionally
                if random.random() < 0.1:  # 10% chance
                    await asyncio.sleep(random.uniform(0.2, 0.5))
                    
        except Exception as e:
            logger.error(f"Error in human-like typing: {e}")
            # Fallback to regular typing
            element.send_keys(text)
    
    async def submit_form(self) -> bool:
        """Submit the form"""
        try:
            logger.info("Submitting form")
            
            # Look for submit button
            submit_selectors = [
                "//button[@type='submit']",
                "//input[@type='submit']",
                "//button[contains(text(), 'Submit')]",
                "//button[contains(text(), 'Send')]"
            ]
            
            for selector in submit_selectors:
                try:
                    submit_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    
                    # Scroll to button
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    await self.human_delay(1, 2)
                    
                    # Click submit
                    submit_button.click()
                    logger.info("Submit button clicked")
                    
                    # Wait for submission to complete
                    await self.human_delay(5, 8)
                    
                    # Check for success indicators
                    if self.check_submission_success():
                        logger.info("Form submission successful")
                        return True
                    else:
                        logger.warning("Could not verify submission success")
                        return True  # Assume success if no error
                        
                except Exception as e:
                    logger.debug(f"Submit selector {selector} failed: {e}")
                    continue
            
            logger.error("Could not find submit button")
            return False
            
        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    def check_submission_success(self) -> bool:
        """Check for submission success indicators"""
        try:
            success_indicators = [
                "//text()[contains(., 'success')]",
                "//text()[contains(., 'submitted')]",
                "//text()[contains(., 'received')]",
                "//*[contains(@class, 'success')]",
                "//*[contains(@class, 'confirmation')]"
            ]
            
            for indicator in success_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element:
                        logger.info(f"Found success indicator: {indicator}")
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking submission success: {e}")
            return False
    
    async def simulate_human_behavior(self):
        """Simulate realistic human browsing behavior"""
        try:
            # Random mouse movements and scrolling
            actions = ActionChains(self.driver)
            
            # Scroll randomly
            for _ in range(random.randint(1, 3)):
                self.driver.execute_script(f"window.scrollBy(0, {random.randint(100, 500)});")
                await self.human_delay(0.5, 1.5)
            
            # Random mouse movements
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                actions.move_by_offset(x, y).perform()
                await self.human_delay(0.3, 0.8)
                
        except Exception as e:
            logger.warning(f"Error in human behavior simulation: {e}")
    
    async def human_delay(self, min_seconds, max_seconds):
        """Add human-like delay"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def reset_driver(self):
        """Reset the driver for retry attempts"""
        try:
            logger.info("Resetting undetected Chrome driver")
            if self.driver:
                self.driver.quit()
            await asyncio.sleep(2)
            await self.initialize()
        except Exception as e:
            logger.error(f"Error resetting driver: {e}")
            raise
    
    async def close(self):
        """Close the driver"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("Undetected Chrome driver closed")
        except Exception as e:
            logger.error(f"Error closing driver: {e}")
