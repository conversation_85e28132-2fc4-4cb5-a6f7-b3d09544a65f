#!/usr/bin/env python3
"""
Test script for the enhanced anti-detection scraper
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.scraper.minecraft_scraper import MinecraftScraper
from src.database.models import TicketData, TicketStatus


async def test_enhanced_scraper():
    """Test the enhanced anti-detection scraper"""
    print("🚀 Testing Enhanced Anti-Detection Scraper")
    print("=" * 50)
    
    # Create test ticket data
    ticket_data = TicketData(
        user_id=12345,
        ticket_type="Technical Issue",
        subject="Test Enhanced Scraper",
        description="Testing the enhanced anti-detection scraper with realistic behavior patterns and stealth measures.",
        email="<EMAIL>",
        minecraft_username="TestUser123"
    )
    
    scraper = MinecraftScraper()
    
    try:
        print("🔧 Initializing enhanced browser with anti-detection...")
        await scraper.initialize()
        print("✅ Enhanced browser initialized successfully")
        
        print("🌐 Testing stealth navigation...")
        await scraper.navigate_to_ticket_form()
        print("✅ Navigation completed successfully")
        
        print("🔍 Verifying form presence...")
        form_verified = await scraper.verify_ticket_form()
        if form_verified:
            print("✅ Form verification successful")
        else:
            print("❌ Form verification failed")
            return False
            
        print("📝 Testing enhanced form filling...")
        form_filled = await scraper.fill_support_form(ticket_data)
        if form_filled:
            print("✅ Form filling successful")
        else:
            print("❌ Form filling failed")
            return False
            
        print("🎯 Testing form submission...")
        # For testing, we'll skip actual submission to avoid spam
        print("⚠️  Skipping actual submission for testing purposes")
        
        print("✅ Enhanced scraper test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced scraper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        print("🧹 Cleaning up...")
        await scraper.close()
        print("✅ Cleanup complete")


async def test_full_automation():
    """Test the full automation workflow"""
    print("\n🤖 Testing Full Automation Workflow")
    print("=" * 50)
    
    # Create test ticket data
    ticket_data = TicketData(
        user_id=67890,
        ticket_type="Account Issue",
        subject="Automated Test Ticket",
        description="This is a test of the fully automated ticket submission system with enhanced anti-detection measures.",
        email="<EMAIL>",
        minecraft_username="AutoTestUser"
    )
    
    scraper = MinecraftScraper()
    
    try:
        print("🚀 Starting full automation test...")
        
        # Test the complete submit_ticket workflow
        # Note: This will attempt actual submission, so we'll modify it for testing
        success = await scraper.submit_ticket(ticket_data)
        
        if success:
            print("✅ Full automation test successful!")
            return True
        else:
            print("❌ Full automation test failed")
            return False
            
    except Exception as e:
        print(f"❌ Full automation test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all enhanced scraper tests"""
    print("🧪 MC-Ticketer Enhanced Anti-Detection Test Suite")
    print("=" * 60)
    
    # Test enhanced scraper components
    scraper_success = await test_enhanced_scraper()
    
    # Test full automation workflow
    automation_success = await test_full_automation()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"Enhanced Scraper: {'✅ PASS' if scraper_success else '❌ FAIL'}")
    print(f"Full Automation: {'✅ PASS' if automation_success else '❌ FAIL'}")
    
    if scraper_success and automation_success:
        print("🎉 All enhanced anti-detection tests passed!")
        print("🚀 MC-Ticketer is ready for fully automated operation!")
        return 0
    else:
        print("❌ Some tests failed - automation may need further refinement")
        return 1


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Test suite failed with exception: {e}")
        sys.exit(1)
