"""
Web scraper for Minecraft support system using Selenium-Driverless
"""
import asyncio
import random
from typing import Optional
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
from loguru import logger
from src.config import settings
from src.database.models import TicketData


class MinecraftScraper:
    """Web scraper for Minecraft support ticket submission using Selenium-Driverless"""

    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.target = None

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self):
        """Initialize the browser with advanced anti-detection measures using Selenium-Driverless"""
        try:
            # Configure Chrome options for maximum stealth
            options = webdriver.ChromeOptions()

            # Basic anti-detection arguments
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--no-first-run')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--metrics-recording-only')
            options.add_argument('--no-report-upload')
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')

            # Set headless mode if configured
            if settings.headless_mode:
                options.add_argument('--headless=new')

            # Set realistic window size
            width = random.randint(1366, 1920)
            height = random.randint(768, 1080)
            options.add_argument(f'--window-size={width},{height}')

            # Initialize selenium-driverless Chrome
            self.driver = webdriver.Chrome(options=options)
            await self.driver.start_session()

            # Get the current target (tab)
            self.target = await self.driver.current_target

            # Setup anti-detection measures
            await self.setup_stealth_mode()

            logger.info("Selenium-Driverless browser initialized successfully with anti-detection measures")

        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def setup_stealth_mode(self):
        """Setup stealth mode to avoid detection using Selenium-Driverless"""
        try:
            # Execute JavaScript to override navigator properties
            await self.target.execute_script("""
                // Override webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // Override plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        },
                        {
                            0: {type: "application/pdf", suffixes: "pdf", description: ""},
                            description: "",
                            filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                            length: 1,
                            name: "Chrome PDF Viewer"
                        }
                    ],
                });

                // Override languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                // Override platform
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32',
                });

                // Override hardwareConcurrency
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 8,
                });

                // Override deviceMemory
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8,
                });

                // Override permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)

            logger.info("Stealth mode configured successfully")

        except Exception as e:
            logger.error(f"Error setting up stealth mode: {e}")

    async def submit_ticket(self, ticket_data: TicketData) -> bool:
        """Submit a support ticket using Selenium-Driverless"""
        logger.info(f"Starting ticket submission for {ticket_data.ticket_id} using Selenium-Driverless")

        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                logger.info(f"Submission attempt {attempt + 1}/{max_attempts}")

                # Initialize browser if not already done
                if not self.driver:
                    await self.initialize()

                # Navigate to ticket form with stealth
                await self.navigate_to_ticket_form()

                # Verify we're on the correct form
                if not await self.verify_ticket_form():
                    logger.warning("Could not verify ticket form")
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(3)
                        continue
                    return False

                # Fill out the support form with human-like behavior
                form_filled = await self.fill_support_form(ticket_data)
                if not form_filled:
                    logger.warning("Failed to fill support form")
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(3)
                        continue
                    return False

                # Submit the form
                submitted = await self.submit_form()
                if not submitted:
                    logger.warning("Failed to submit form")
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(3)
                        continue
                    return False

                logger.info(f"Successfully submitted ticket {ticket_data.ticket_id}")
                return True

            except Exception as e:
                logger.warning(f"Submission attempt {attempt + 1} failed: {e}")
                if attempt < max_attempts - 1:
                    await self.reset_driver()
                    await asyncio.sleep(3)
                    continue
                else:
                    return False

        logger.error(f"All submission attempts failed for ticket {ticket_data.ticket_id}")
        return False

    async def reset_driver(self):
        """Reset driver instance for retry attempts"""
        try:
            logger.info("Resetting driver for retry attempt")
            await self.close()
            await asyncio.sleep(2)  # Brief pause
            await self.initialize()
        except Exception as e:
            logger.error(f"Error resetting driver: {e}")
            raise
    
    async def navigate_to_ticket_form(self):
        """Navigate to Minecraft ticket submission form with stealth measures"""
        logger.info("Navigating to Minecraft ticket submission form using Selenium-Driverless")

        # Primary target URL
        target_url = "https://help.minecraft.net/hc/en-us/requests/new"

        try:
            # Step 1: Simulate realistic browsing behavior
            await self.simulate_realistic_browsing()

            # Step 2: Navigate to main help center first (more realistic)
            logger.info("First navigating to main help center...")
            await self.target.get("https://help.minecraft.net/", wait_load=True)
            logger.info("Successfully reached main help center")

            # Random delay to appear human
            await asyncio.sleep(random.uniform(2, 4))

            # Perform some realistic interactions
            await self.random_mouse_movement()
            await self.scroll_randomly()
            await asyncio.sleep(random.uniform(1, 3))

            # Step 3: Navigate to the ticket form
            logger.info(f"Now navigating to ticket form: {target_url}")
            await self.target.get(target_url, wait_load=True)
            logger.info("Successfully navigated to ticket form")

            # Wait for page to fully load
            await asyncio.sleep(random.uniform(3, 5))

            # Check if we're on the right page
            title = await self.target.title
            current_url = await self.target.current_url
            logger.info(f"Page title: {title}")
            logger.info(f"Current URL: {current_url}")

            # Perform additional realistic behavior
            await self.random_mouse_movement()
            await asyncio.sleep(random.uniform(1, 2))

        except Exception as e:
            logger.error(f"Navigation failed: {e}")

            # Fallback: Try direct navigation
            logger.info("Trying fallback direct navigation...")
            await self.fallback_navigation(target_url)

    async def simulate_realistic_browsing(self):
        """Simulate realistic browsing patterns before accessing the target"""
        try:
            logger.info("Simulating realistic browsing behavior")

            # Visit a common website first to establish browsing history
            common_sites = [
                "https://www.google.com",
                "https://www.bing.com",
                "https://www.minecraft.net"
            ]

            site = random.choice(common_sites)
            logger.info(f"Pre-browsing to: {site}")

            try:
                await self.target.get(site, wait_load=True)
                await asyncio.sleep(random.uniform(1, 3))
                await self.random_mouse_movement()
                await self.scroll_randomly()
                await asyncio.sleep(random.uniform(0.5, 1.5))
            except Exception as e:
                logger.warning(f"Pre-browsing failed, continuing anyway: {e}")

        except Exception as e:
            logger.warning(f"Error in realistic browsing simulation: {e}")

    async def fallback_navigation(self, target_url):
        """Fallback navigation method with different strategy"""
        try:
            logger.info("Attempting fallback navigation strategy")

            # Try direct navigation to target URL with different strategies
            alternative_urls = [
                target_url,
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=360003469452",
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=4416074743565"
            ]

            for url in alternative_urls:
                try:
                    logger.info(f"Trying direct navigation to: {url}")
                    await self.target.get(url, wait_load=True)
                    logger.info(f"Fallback navigation successful to {url}")
                    await asyncio.sleep(random.uniform(2, 4))
                    return

                except Exception as e:
                    logger.warning(f"Fallback navigation failed for {url}: {e}")
                    continue

            raise Exception("All fallback navigation strategies and URLs failed")

        except Exception as e:
            logger.error(f"Fallback navigation failed: {e}")
            raise

    async def random_mouse_movement(self):
        """Perform random mouse movements to appear human"""
        try:
            # Get viewport size (use default if not available)
            viewport_width = 1366
            viewport_height = 768

            # Random coordinates within viewport
            x = random.randint(100, viewport_width - 100)
            y = random.randint(100, viewport_height - 100)

            # Move mouse to random position using pointer
            await self.target.pointer.move_to(x, y, total_time=random.uniform(0.5, 1.5))
            await asyncio.sleep(random.uniform(0.1, 0.5))

        except Exception as e:
            logger.error(f"Error with mouse movement: {e}")

    async def scroll_randomly(self):
        """Perform random scrolling to appear human"""
        try:
            # Random scroll amount and direction
            scroll_amount = random.randint(100, 500)
            direction = random.choice(['up', 'down'])

            if direction == 'down':
                await self.target.execute_script(f"window.scrollBy(0, {scroll_amount});")
            else:
                await self.target.execute_script(f"window.scrollBy(0, -{scroll_amount});")

            await asyncio.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            logger.error(f"Error with scrolling: {e}")

    async def human_like_delay(self, min_seconds: float = 0.5, max_seconds: float = 2.0):
        """Add human-like delay between actions"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)

    async def verify_ticket_form(self) -> bool:
        """Verify we're on the ticket submission form"""
        logger.info("Verifying ticket submission form")

        try:
            # Check for common form elements
            form_indicators = [
                'form',
                'input[type="email"]',
                'textarea',
                'select'
            ]

            for indicator in form_indicators:
                try:
                    elements = await self.target.find_elements(By.CSS_SELECTOR, indicator, timeout=5)
                    if elements:
                        logger.info(f"Found form element: {indicator} ({len(elements)} elements)")
                        return True
                except:
                    continue

            # Check URL to confirm we're on the right page
            current_url = await self.target.current_url
            if "request" in current_url.lower() and ("new" in current_url.lower() or "form" in current_url.lower()):
                logger.info("URL confirms we're on ticket submission page")
                return True

            logger.warning("Could not verify ticket form presence")
            return False

        except Exception as e:
            logger.error(f"Error verifying ticket form: {e}")
            return False

    async def simulate_form_inspection(self):
        """Simulate realistic form inspection behavior"""
        try:
            logger.info("Simulating form inspection behavior")

            # Random mouse movements to "look around" the form
            for _ in range(random.randint(2, 4)):
                await self.random_mouse_movement()
                await asyncio.sleep(random.uniform(0.5, 1.5))

            # Scroll to see the form better
            await self.scroll_randomly()
            await asyncio.sleep(random.uniform(1, 2))

            # More mouse movements
            await self.random_mouse_movement()
            await asyncio.sleep(random.uniform(0.5, 1))

        except Exception as e:
            logger.warning(f"Error in form inspection simulation: {e}")
    
    async def fill_support_form(self, ticket_data: TicketData) -> bool:
        """Fill out the support form with human-like behavior using Selenium-Driverless"""
        logger.info("Filling support form with stealth measures")

        try:
            # Wait for form to be fully loaded with random delay
            await asyncio.sleep(random.uniform(3, 5))

            # Simulate realistic user behavior before filling form
            await self.simulate_form_inspection()

            # Minecraft-specific form field selectors
            form_fields = {
                'email': [
                    'input[type="email"]',
                    'input[name*="email"]',
                    '#email',
                    'input[id*="email"]'
                ],
                'subject': [
                    'input[name*="subject"]',
                    '#subject',
                    'input[placeholder*="subject"]',
                    'input[id*="subject"]'
                ],
                'description': [
                    'textarea',
                    'textarea[name*="description"]',
                    '#description',
                    'textarea[id*="description"]',
                    'textarea[placeholder*="describe"]'
                ]
            }

            # Fill email with human-like typing
            email_filled = await self.fill_field_with_stealth(
                form_fields['email'],
                ticket_data.email,
                "email"
            )

            # Random delay between fields
            await asyncio.sleep(random.uniform(1, 3))
            await self.random_mouse_movement()

            # Fill subject with enhanced human-like typing
            subject_filled = await self.fill_field_with_stealth(
                form_fields['subject'],
                ticket_data.subject,
                "subject"
            )

            # Random delay between fields
            await asyncio.sleep(random.uniform(1, 3))
            await self.random_mouse_movement()

            # Fill description with enhanced human-like typing
            full_description = f"Issue Type: {ticket_data.ticket_type}\n\n"
            full_description += f"Description:\n{ticket_data.description}\n\n"

            if ticket_data.minecraft_username:
                full_description += f"Minecraft Username: {ticket_data.minecraft_username}\n\n"

            full_description += "Additional Information: This ticket contains all relevant details for support assistance."

            description_filled = await self.fill_field_with_stealth(
                form_fields['description'],
                full_description,
                "description"
            )

            # Final verification that all required fields are filled
            filled_count = sum([email_filled, subject_filled, description_filled])
            logger.info(f"Form filling summary: {filled_count}/3 required fields filled")

            if filled_count >= 2:  # At least email and description are critical
                logger.info("Form filling completed successfully")
                return True
            else:
                logger.error("Failed to fill minimum required fields")
                return False
            
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return False

    async def fill_field_with_stealth(self, selectors, value, field_name):
        """Fill a form field with advanced stealth measures using Selenium-Driverless"""
        try:
            for selector in selectors:
                try:
                    # Wait for field with random timeout
                    timeout = random.randint(3, 7)
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=timeout)

                    if elements:
                        field = elements[0]  # Use first found element

                        # Simulate realistic field interaction
                        await self.simulate_field_focus(field)

                        # Clear field first (realistic behavior)
                        await field.click()
                        await asyncio.sleep(random.uniform(0.2, 0.5))
                        await field.clear()
                        await asyncio.sleep(random.uniform(0.1, 0.3))

                        # Type with realistic human patterns
                        await self.type_like_human(field, value)

                        logger.info(f"Successfully filled {field_name} field using selector: {selector}")

                        # Random delay after filling
                        await asyncio.sleep(random.uniform(0.5, 1.5))
                        return True

                except Exception as e:
                    logger.debug(f"{field_name} selector {selector} failed: {e}")
                    continue

            logger.warning(f"Could not find {field_name} field")
            return False

        except Exception as e:
            logger.error(f"Error filling {field_name} field: {e}")
            return False

    async def simulate_field_focus(self, field):
        """Simulate realistic field focusing behavior"""
        try:
            # Move to field and click
            await field.click(move_to=True)
            await asyncio.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            logger.debug(f"Error in field focus simulation: {e}")

    async def type_like_human(self, field, text):
        """Type text with realistic human patterns"""
        try:
            # Split text into chunks for more realistic typing
            words = text.split(' ')

            for i, word in enumerate(words):
                # Type each character with random delays
                for char in word:
                    await field.send_keys(char)
                    # Random delay between characters (50-200ms)
                    await asyncio.sleep(random.uniform(0.05, 0.2))

                # Add space between words (except last word)
                if i < len(words) - 1:
                    await field.send_keys(' ')
                    # Slightly longer delay after spaces
                    await asyncio.sleep(random.uniform(0.1, 0.3))

                # Random pause between words
                if random.random() < 0.3:  # 30% chance of pause
                    await asyncio.sleep(random.uniform(0.3, 0.8))

        except Exception as e:
            logger.error(f"Error in human-like typing: {e}")
            # Fallback to regular typing
            await field.write(text)

    async def submit_form(self) -> bool:
        """Submit the support form"""
        logger.info("Submitting form")

        try:
            # Look for submit button
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                '.submit-button',
                '#submit'
            ]

            for selector in submit_selectors:
                try:
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=3)
                    if elements:
                        submit_button = elements[0]
                        await submit_button.click(move_to=True)
                        logger.info("Clicked submit button")

                        # Wait for submission to complete
                        await asyncio.sleep(5)

                        # Check for success indicators
                        success_indicators = [
                            'success',
                            'submitted',
                            'received',
                            'confirmation'
                        ]

                        for indicator in success_indicators:
                            try:
                                # Check page content for success text
                                page_content = await self.target.execute_script("return document.body.innerText.toLowerCase();")
                                if indicator in page_content:
                                    logger.info(f"Found success indicator: {indicator}")
                                    return True
                            except:
                                continue

                        # If no success indicator found, assume success if no error
                        logger.info("Form submitted successfully (no error detected)")
                        return True
                except Exception as e:
                    logger.debug(f"Submit selector {selector} failed: {e}")
                    continue

            logger.error("Could not find submit button")
            return False
            
        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.driver:
                await self.driver.quit()
                self.driver = None
                self.target = None

            logger.info("Selenium-Driverless browser closed successfully")

        except Exception as e:
            logger.error(f"Error closing browser: {e}")


