"""
Web scraper for Minecraft support system
"""
import asyncio
import random
import j<PERSON>
from typing import <PERSON><PERSON>
from playwright.async_api import async_playwright, <PERSON>rowser, Page
try:
    from playwright_stealth import Stealth
    stealth_available = True
except ImportError:
    stealth_available = False
from loguru import logger
from src.config import settings
from src.database.models import TicketData
from src.scraper.anti_bot import AntiBot


class MinecraftScraper:
    """Web scraper for Minecraft support ticket submission"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.anti_bot: Optional[AntiBot] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self):
        """Initialize the browser with advanced anti-detection measures"""
        try:
            self.playwright = await async_playwright().start()

            # Advanced browser configuration to avoid detection
            self.browser = await self.playwright.chromium.launch(
                headless=settings.headless_mode,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-client-side-phishing-detection',
                    '--disable-sync',
                    '--metrics-recording-only',
                    '--no-report-upload',
                    '--disable-web-security',
                    '--allow-running-insecure-content',
                    '--disable-extensions-file-access-check',
                    '--disable-extensions-http-throttling',
                    '--disable-component-extensions-with-background-pages'
                ],
                slow_mo=random.randint(50, 150)  # Random delay between actions
            )

            # Create realistic browser context
            context = await self.browser.new_context(
                user_agent=self.get_random_user_agent(),
                viewport={'width': random.randint(1366, 1920), 'height': random.randint(768, 1080)},
                locale='en-US',
                timezone_id='America/New_York',
                permissions=['geolocation'],
                geolocation={'latitude': 40.7128, 'longitude': -74.0060},  # New York
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Cache-Control': 'max-age=0'
                }
            )

            self.page = await context.new_page()

            # Apply playwright-stealth if available
            if stealth_available:
                stealth_obj = Stealth()
                await stealth_obj.apply_stealth_async(self.page)
                logger.info("Applied playwright-stealth measures")
            else:
                logger.info("Using manual stealth measures only")

            # Set realistic timeouts
            self.page.set_default_timeout(45000)  # 45 seconds

            # Initialize enhanced anti-bot measures
            self.anti_bot = AntiBot(self.page)
            await self.anti_bot.setup_stealth_mode()

            # Add realistic browser behavior
            await self.setup_realistic_behavior()

            logger.info("Advanced anti-detection browser initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def submit_ticket(self, ticket_data: TicketData) -> bool:
        """Submit a support ticket with hybrid approach (Playwright + fallback to undetected Chrome)"""
        logger.info(f"Starting hybrid ticket submission for {ticket_data.ticket_id}")

        # First attempt: Try Playwright with advanced stealth
        playwright_success = await self.try_playwright_submission(ticket_data)
        if playwright_success:
            logger.info(f"Successfully submitted ticket {ticket_data.ticket_id} using Playwright")
            return True

        # Second attempt: Fallback to undetected Chrome
        logger.info("Playwright submission failed, trying undetected Chrome fallback")
        undetected_success = await self.try_undetected_submission(ticket_data)
        if undetected_success:
            logger.info(f"Successfully submitted ticket {ticket_data.ticket_id} using undetected Chrome")
            return True

        logger.error(f"All submission methods failed for ticket {ticket_data.ticket_id}")
        return False

    async def try_playwright_submission(self, ticket_data: TicketData) -> bool:
        """Try submission using Playwright with stealth measures"""
        max_attempts = 2

        for attempt in range(max_attempts):
            try:
                logger.info(f"Playwright submission attempt {attempt + 1}/{max_attempts}")

                # Initialize browser with anti-detection measures
                if not self.browser:
                    await self.initialize()

                # Navigate to ticket form with stealth
                await self.navigate_to_ticket_form()

                # Verify we're on the correct form
                if not await self.verify_ticket_form():
                    logger.warning("Could not verify ticket form with Playwright")
                    if attempt < max_attempts - 1:
                        await self.reset_browser()
                        await asyncio.sleep(3)
                        continue
                    return False

                # Fill out the support form with human-like behavior
                form_filled = await self.fill_support_form(ticket_data)
                if not form_filled:
                    logger.warning("Failed to fill support form with Playwright")
                    if attempt < max_attempts - 1:
                        await self.reset_browser()
                        await asyncio.sleep(3)
                        continue
                    return False

                # Submit the form
                submitted = await self.submit_form()
                if not submitted:
                    logger.warning("Failed to submit form with Playwright")
                    if attempt < max_attempts - 1:
                        await self.reset_browser()
                        await asyncio.sleep(3)
                        continue
                    return False

                logger.info("Playwright submission successful")
                return True

            except Exception as e:
                logger.warning(f"Playwright submission attempt {attempt + 1} failed: {e}")
                if attempt < max_attempts - 1:
                    await self.reset_browser()
                    await asyncio.sleep(3)
                    continue
                else:
                    return False

        return False

    async def try_undetected_submission(self, ticket_data: TicketData) -> bool:
        """Try submission using undetected Chrome as fallback"""
        try:
            from src.scraper.undetected_scraper import UndetectedMinecraftScraper

            logger.info("Initializing undetected Chrome scraper")
            undetected_scraper = UndetectedMinecraftScraper()

            try:
                success = await undetected_scraper.submit_ticket(ticket_data)
                return success
            finally:
                await undetected_scraper.close()

        except Exception as e:
            logger.error(f"Undetected Chrome submission failed: {e}")
            return False
    
    async def navigate_to_ticket_form(self):
        """Navigate to Minecraft ticket submission form with advanced stealth"""
        logger.info("Navigating to Minecraft ticket submission form with stealth measures")

        # Primary target URL
        target_url = "https://help.minecraft.net/hc/en-us/requests/new"

        try:
            # Step 1: Simulate realistic browsing behavior
            await self.simulate_realistic_browsing()

            # Step 2: Navigate to main help center first (more realistic)
            logger.info("First navigating to main help center...")
            response = await self.page.goto("https://help.minecraft.net/", timeout=30000)

            if response and response.status < 400:
                logger.info("Successfully reached main help center")

                # Random delay to appear human
                await asyncio.sleep(random.uniform(2, 4))

                # Perform some realistic interactions
                await self.anti_bot.random_mouse_movement()
                await self.anti_bot.scroll_randomly()
                await asyncio.sleep(random.uniform(1, 3))

                # Step 3: Navigate to the ticket form
                logger.info(f"Now navigating to ticket form: {target_url}")
                response = await self.page.goto(target_url, timeout=30000)

                if response and response.status < 400:
                    logger.info(f"Successfully navigated to ticket form (status: {response.status})")

                    # Wait for page to fully load
                    await asyncio.sleep(random.uniform(3, 5))

                    # Check if we're on the right page
                    title = await self.page.title()
                    current_url = self.page.url
                    logger.info(f"Page title: {title}")
                    logger.info(f"Current URL: {current_url}")

                    # Perform additional realistic behavior
                    await self.anti_bot.random_mouse_movement()
                    await asyncio.sleep(random.uniform(1, 2))

                    return
                else:
                    raise Exception(f"Failed to reach ticket form: status {response.status if response else 'No response'}")
            else:
                raise Exception(f"Failed to reach main help center: status {response.status if response else 'No response'}")

        except Exception as e:
            logger.error(f"Navigation failed: {e}")

            # Fallback: Try direct navigation with different approach
            logger.info("Trying fallback direct navigation...")
            await self.fallback_navigation(target_url)

    async def simulate_realistic_browsing(self):
        """Simulate realistic browsing patterns before accessing the target"""
        try:
            logger.info("Simulating realistic browsing behavior")

            # Visit a common website first to establish browsing history
            common_sites = [
                "https://www.google.com",
                "https://www.bing.com",
                "https://www.minecraft.net"
            ]

            site = random.choice(common_sites)
            logger.info(f"Pre-browsing to: {site}")

            try:
                await self.page.goto(site, timeout=15000)
                await asyncio.sleep(random.uniform(1, 3))
                await self.anti_bot.random_mouse_movement()
                await self.anti_bot.scroll_randomly()
                await asyncio.sleep(random.uniform(0.5, 1.5))
            except Exception as e:
                logger.warning(f"Pre-browsing failed, continuing anyway: {e}")

        except Exception as e:
            logger.warning(f"Error in realistic browsing simulation: {e}")

    async def fallback_navigation(self, target_url):
        """Fallback navigation method with different strategy"""
        try:
            logger.info("Attempting fallback navigation strategy")

            # Skip localStorage clearing to avoid security errors
            logger.info("Skipping localStorage clearing due to security restrictions")

            # Try direct navigation to target URL with different strategies
            alternative_urls = [
                target_url,
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=360003469452",
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=4416074743565"
            ]

            for url in alternative_urls:
                logger.info(f"Trying direct navigation to: {url}")

                # Try with different wait conditions
                wait_strategies = [
                    ("domcontentloaded", 15000),
                    ("load", 20000),
                    ("networkidle", 25000),
                    (None, 10000)  # No wait condition
                ]

                for strategy, timeout in wait_strategies:
                    try:
                        logger.info(f"Trying navigation with wait strategy: {strategy}, timeout: {timeout}ms")

                        if strategy:
                            response = await self.page.goto(url, wait_until=strategy, timeout=timeout)
                        else:
                            response = await self.page.goto(url, timeout=timeout)

                        if response and response.status < 400:
                            logger.info(f"Fallback navigation successful to {url} with {strategy}")
                            await asyncio.sleep(random.uniform(2, 4))
                            return

                    except Exception as e:
                        logger.warning(f"Fallback strategy {strategy} failed for {url}: {e}")
                        continue

            raise Exception("All fallback navigation strategies and URLs failed")

        except Exception as e:
            logger.error(f"Fallback navigation failed: {e}")
            raise

    async def navigate_to_ticket_form_quick(self):
        """Quick navigation attempt with short timeout"""
        logger.info("Attempting quick navigation to ticket form")

        # Try just the main URL with a very short timeout
        ticket_url = "https://help.minecraft.net/hc/en-us/requests/new"

        try:
            response = await self.page.goto(ticket_url, timeout=5000)  # 5 second timeout

            if response and response.status < 400:
                logger.info(f"Quick navigation successful to {ticket_url}")
                await asyncio.sleep(1)  # Brief wait
                return
            else:
                raise Exception(f"Bad response status: {response.status if response else 'No response'}")

        except Exception as e:
            logger.warning(f"Quick navigation failed: {e}")
            raise

    
    async def verify_ticket_form(self) -> bool:
        """Verify we're on the ticket submission form"""
        logger.info("Verifying ticket submission form")

        try:
            # Check for common form elements
            form_indicators = [
                'form',
                'input[type="email"]',
                'textarea',
                'select',
                '[data-test-id*="ticket"]',
                '[class*="request"]',
                '[class*="form"]'
            ]

            for indicator in form_indicators:
                try:
                    element = await self.page.wait_for_selector(indicator, timeout=5000)
                    if element:
                        logger.info(f"Found form element: {indicator}")
                        return True
                except:
                    continue

            # Check URL to confirm we're on the right page
            current_url = self.page.url
            if "requests/new" in current_url:
                logger.info("URL confirms we're on ticket submission page")
                return True

            logger.warning("Could not verify ticket form presence")
            return False

        except Exception as e:
            logger.error(f"Error verifying ticket form: {e}")
            return False

    async def find_contact_support(self) -> bool:
        """Find and click contact support link"""
        logger.info("Looking for contact support option")
        
        try:
            # Look for various contact support patterns
            contact_selectors = [
                'a[href*="contact"]',
                'a[href*="support"]',
                'button:has-text("Contact")',
                'a:has-text("Contact Support")',
                'a:has-text("Contact Us")',
                '.contact-support',
                '#contact-support'
            ]
            
            for selector in contact_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        logger.info(f"Found contact element with selector: {selector}")
                        await element.click()
                        await asyncio.sleep(2)
                        return True
                except:
                    continue
            
            # If no direct contact link found, try searching for help articles
            # that might have contact support at the bottom
            await self.search_for_support_article()
            return True
            
        except Exception as e:
            logger.error(f"Error finding contact support: {e}")
            return False
    
    async def search_for_support_article(self):
        """Search for a support article that has contact support"""
        logger.info("Searching for support article with contact option")
        
        try:
            # Look for search box
            search_selectors = [
                'input[type="search"]',
                'input[placeholder*="search"]',
                '.search-input',
                '#search'
            ]
            
            for selector in search_selectors:
                try:
                    search_box = await self.page.wait_for_selector(selector, timeout=3000)
                    if search_box:
                        await search_box.fill("account help")
                        await search_box.press("Enter")
                        await asyncio.sleep(3)
                        
                        # Click on first search result
                        first_result = await self.page.wait_for_selector('a[href*="articles"]', timeout=5000)
                        if first_result:
                            await first_result.click()
                            await asyncio.sleep(2)
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"Error searching for support article: {e}")
    
    async def fill_support_form(self, ticket_data: TicketData) -> bool:
        """Fill out the support form with advanced human-like behavior"""
        logger.info("Filling support form with stealth measures")

        try:
            # Wait for form to be fully loaded with random delay
            await asyncio.sleep(random.uniform(3, 5))

            # Simulate realistic user behavior before filling form
            await self.simulate_form_inspection()

            # Minecraft-specific form field selectors (more comprehensive)
            form_fields = {
                'email': [
                    'input[type="email"]',
                    'input[name*="email"]',
                    '#email',
                    'input[id*="email"]',
                    'input[data-test-id*="email"]'
                ],
                'subject': [
                    'input[name*="subject"]',
                    '#subject',
                    'input[placeholder*="subject"]',
                    'input[id*="subject"]',
                    'input[data-test-id*="subject"]'
                ],
                'description': [
                    'textarea',
                    'textarea[name*="description"]',
                    '#description',
                    'textarea[id*="description"]',
                    'textarea[placeholder*="describe"]',
                    'textarea[data-test-id*="description"]'
                ],
                'name': [
                    'input[name*="name"]',
                    '#name',
                    'input[placeholder*="name"]',
                    'input[id*="name"]'
                ],
                'category': [
                    'select[name*="category"]',
                    'select[name*="type"]',
                    '#category',
                    'select[id*="category"]',
                    'select[data-test-id*="category"]'
                ]
            }
            
            # Fill email with human-like typing
            email_filled = False
            for selector in form_fields['email']:
                try:
                    email_field = await self.page.wait_for_selector(selector, timeout=5000)
                    if email_field:
                        await self.anti_bot.human_like_typing(email_field, ticket_data.email)
                        logger.info(f"Filled email field using selector: {selector}")
                        email_filled = True
                        break
                except Exception as e:
                    logger.debug(f"Email selector {selector} failed: {e}")
                    continue

            if not email_filled:
                logger.warning("Could not find email field")
            
            # Random delay between fields
            await asyncio.sleep(random.uniform(1, 3))
            await self.anti_bot.random_mouse_movement()

            # Fill subject with enhanced human-like typing
            subject_filled = await self.fill_field_with_stealth(
                form_fields['subject'],
                ticket_data.subject,
                "subject"
            )

            # Random delay between fields
            await asyncio.sleep(random.uniform(1, 3))
            await self.anti_bot.random_mouse_movement()

            # Fill description with enhanced human-like typing
            full_description = f"Issue Type: {ticket_data.ticket_type}\n\n"
            full_description += f"Description:\n{ticket_data.description}\n\n"

            if ticket_data.minecraft_username:
                full_description += f"Minecraft Username: {ticket_data.minecraft_username}\n\n"

            full_description += "Additional Information: This ticket contains all relevant details for support assistance."

            description_filled = await self.fill_field_with_stealth(
                form_fields['description'],
                full_description,
                "description"
            )
            
            # Random delay before category selection
            await asyncio.sleep(random.uniform(1, 2))

            # Try to select category if available
            category_filled = await self.select_category_with_stealth(
                form_fields['category'],
                ticket_data.ticket_type
            )

            # Final verification that all required fields are filled
            filled_count = sum([email_filled, subject_filled, description_filled])
            logger.info(f"Form filling summary: {filled_count}/3 required fields filled")

            if filled_count >= 2:  # At least email and description are critical
                logger.info("Form filling completed successfully")
                return True
            else:
                logger.error("Failed to fill minimum required fields")
                return False
            
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return False
    
    async def submit_form(self) -> bool:
        """Submit the support form"""
        logger.info("Submitting form")
        
        try:
            # Look for submit button
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Submit")',
                'button:has-text("Send")',
                '.submit-button',
                '#submit'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if submit_button:
                        await submit_button.click()
                        logger.info("Clicked submit button")
                        
                        # Wait for submission to complete
                        await asyncio.sleep(5)
                        
                        # Check for success indicators
                        success_indicators = [
                            ':has-text("success")',
                            ':has-text("submitted")',
                            ':has-text("received")',
                            '.success',
                            '.confirmation'
                        ]
                        
                        for indicator in success_indicators:
                            try:
                                success_element = await self.page.wait_for_selector(indicator, timeout=5000)
                                if success_element:
                                    logger.info("Found success indicator")
                                    return True
                            except:
                                continue
                        
                        # If no success indicator found, assume success if no error
                        return True
                except:
                    continue
            
            logger.error("Could not find submit button")
            return False
            
        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    def get_random_user_agent(self):
        """Get a random realistic user agent"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]
        return random.choice(user_agents)

    async def setup_realistic_behavior(self):
        """Setup realistic browser behavior patterns"""
        try:
            # Override navigator properties to appear more human
            await self.page.add_init_script("""
                // Override webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // Override plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        },
                        {
                            0: {type: "application/pdf", suffixes: "pdf", description: ""},
                            description: "",
                            filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                            length: 1,
                            name: "Chrome PDF Viewer"
                        }
                    ],
                });

                // Override languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                // Override platform
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32',
                });

                // Override hardwareConcurrency
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 8,
                });

                // Override deviceMemory
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8,
                });

                // Override permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );

                // Add realistic screen properties
                Object.defineProperty(screen, 'availWidth', {
                    get: () => window.screen.width,
                });
                Object.defineProperty(screen, 'availHeight', {
                    get: () => window.screen.height - 40,
                });
            """)

            logger.info("Realistic browser behavior configured")

        except Exception as e:
            logger.error(f"Error setting up realistic behavior: {e}")

    async def reset_browser(self):
        """Reset browser instance for retry attempts"""
        try:
            logger.info("Resetting browser for retry attempt")
            await self.close()
            await asyncio.sleep(2)  # Brief pause
            await self.initialize()
        except Exception as e:
            logger.error(f"Error resetting browser: {e}")
            raise

    async def simulate_form_inspection(self):
        """Simulate realistic form inspection behavior"""
        try:
            logger.info("Simulating form inspection behavior")

            # Random mouse movements to "look around" the form
            for _ in range(random.randint(2, 4)):
                await self.anti_bot.random_mouse_movement()
                await asyncio.sleep(random.uniform(0.5, 1.5))

            # Scroll to see the form better
            await self.anti_bot.scroll_randomly()
            await asyncio.sleep(random.uniform(1, 2))

            # More mouse movements
            await self.anti_bot.random_mouse_movement()
            await asyncio.sleep(random.uniform(0.5, 1))

        except Exception as e:
            logger.warning(f"Error in form inspection simulation: {e}")

    async def fill_field_with_stealth(self, selectors, value, field_name):
        """Fill a form field with advanced stealth measures"""
        try:
            for selector in selectors:
                try:
                    # Wait for field with random timeout
                    timeout = random.randint(3000, 7000)
                    field = await self.page.wait_for_selector(selector, timeout=timeout)

                    if field:
                        # Simulate realistic field interaction
                        await self.simulate_field_focus(field)

                        # Clear field first (realistic behavior)
                        await field.click()
                        await asyncio.sleep(random.uniform(0.2, 0.5))
                        await field.press("Control+a")
                        await asyncio.sleep(random.uniform(0.1, 0.3))

                        # Type with realistic human patterns
                        await self.type_like_human(field, value)

                        logger.info(f"Successfully filled {field_name} field using selector: {selector}")

                        # Random delay after filling
                        await asyncio.sleep(random.uniform(0.5, 1.5))
                        return True

                except Exception as e:
                    logger.debug(f"{field_name} selector {selector} failed: {e}")
                    continue

            logger.warning(f"Could not find {field_name} field")
            return False

        except Exception as e:
            logger.error(f"Error filling {field_name} field: {e}")
            return False

    async def simulate_field_focus(self, field):
        """Simulate realistic field focusing behavior"""
        try:
            # Move mouse to field area first
            box = await field.bounding_box()
            if box:
                # Add some randomness to click position
                x = box['x'] + box['width'] * random.uniform(0.3, 0.7)
                y = box['y'] + box['height'] * random.uniform(0.3, 0.7)

                await self.page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.3))

            # Click to focus
            await field.click()
            await asyncio.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            logger.debug(f"Error in field focus simulation: {e}")

    async def type_like_human(self, field, text):
        """Type text with realistic human patterns"""
        try:
            # Split text into chunks for more realistic typing
            words = text.split(' ')

            for i, word in enumerate(words):
                # Type each character with random delays
                for char in word:
                    await field.type(char)
                    # Random delay between characters (50-200ms)
                    await asyncio.sleep(random.uniform(0.05, 0.2))

                # Add space between words (except last word)
                if i < len(words) - 1:
                    await field.type(' ')
                    # Slightly longer delay after spaces
                    await asyncio.sleep(random.uniform(0.1, 0.3))

                # Random pause between words
                if random.random() < 0.3:  # 30% chance of pause
                    await asyncio.sleep(random.uniform(0.3, 0.8))

        except Exception as e:
            logger.error(f"Error in human-like typing: {e}")
            # Fallback to regular typing
            await field.fill(text)

    async def select_category_with_stealth(self, selectors, ticket_type):
        """Select category dropdown with stealth measures"""
        try:
            for selector in selectors:
                try:
                    category_field = await self.page.wait_for_selector(selector, timeout=5000)
                    if category_field:
                        # Simulate realistic dropdown interaction
                        await self.simulate_field_focus(category_field)

                        # Try to select based on ticket type
                        try:
                            await category_field.select_option(label=ticket_type)
                            logger.info(f"Selected category: {ticket_type}")
                            return True
                        except:
                            # Try selecting by value or index if label fails
                            try:
                                await category_field.select_option(index=1)  # Select first option
                                logger.info("Selected default category option")
                                return True
                            except:
                                logger.warning("Could not select any category option")

                except Exception as e:
                    logger.debug(f"Category selector {selector} failed: {e}")
                    continue

            logger.warning("Could not find category field")
            return False

        except Exception as e:
            logger.error(f"Error selecting category: {e}")
            return False

    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            logger.info("Browser closed successfully")

        except Exception as e:
            logger.error(f"Error closing browser: {e}")
