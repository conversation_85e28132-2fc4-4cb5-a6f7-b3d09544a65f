"""
Anti-bot countermeasures for web scraping
"""
import asyncio
import random
from typing import <PERSON><PERSON>
from playwright.async_api import Page
from loguru import logger


class AntiBot:
    """Anti-bot detection and countermeasures"""
    
    def __init__(self, page: Page):
        self.page = page
    
    async def setup_stealth_mode(self):
        """Setup stealth mode to avoid detection"""
        try:
            # Remove webdriver property
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # Override plugins
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
            
            # Override languages
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            # Override permissions
            await self.page.add_init_script("""
                const originalQuery = window.navigator.permissions.query;
                return window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)
            
            logger.info("Stealth mode configured")
            
        except Exception as e:
            logger.error(f"Error setting up stealth mode: {e}")
    
    async def human_like_delay(self, min_ms: int = 500, max_ms: int = 2000):
        """Add human-like delay between actions"""
        delay = random.randint(min_ms, max_ms) / 1000
        await asyncio.sleep(delay)
    
    async def human_like_typing(self, element, text: str):
        """Type text with human-like delays"""
        await element.click()
        await self.human_like_delay(100, 300)
        
        for char in text:
            await element.type(char)
            # Random delay between keystrokes
            await asyncio.sleep(random.uniform(0.05, 0.15))
    
    async def random_mouse_movement(self):
        """Perform random mouse movements to appear human"""
        try:
            # Get viewport size
            viewport = self.page.viewport_size
            if not viewport:
                return
            
            # Random coordinates within viewport
            x = random.randint(100, viewport['width'] - 100)
            y = random.randint(100, viewport['height'] - 100)
            
            # Move mouse to random position
            await self.page.mouse.move(x, y)
            await self.human_like_delay(100, 500)
            
        except Exception as e:
            logger.error(f"Error with mouse movement: {e}")
    
    async def detect_captcha(self) -> bool:
        """Detect if CAPTCHA is present on the page"""
        captcha_selectors = [
            'iframe[src*="recaptcha"]',
            'iframe[src*="hcaptcha"]',
            '.g-recaptcha',
            '.h-captcha',
            '[data-sitekey]',
            'img[alt*="captcha"]',
            'img[alt*="CAPTCHA"]',
            '.captcha',
            '#captcha'
        ]
        
        for selector in captcha_selectors:
            try:
                element = await self.page.wait_for_selector(selector, timeout=2000)
                if element:
                    logger.warning(f"CAPTCHA detected with selector: {selector}")
                    return True
            except:
                continue
        
        return False
    
    async def handle_captcha(self) -> bool:
        """Handle CAPTCHA if detected"""
        if not await self.detect_captcha():
            return True
        
        logger.warning("CAPTCHA detected - manual intervention required")
        
        # For now, we'll wait and hope it's solved manually
        # In a production system, you might integrate with CAPTCHA solving services
        
        # Wait for CAPTCHA to be solved (up to 2 minutes)
        for _ in range(24):  # 24 * 5 seconds = 2 minutes
            await asyncio.sleep(5)
            if not await self.detect_captcha():
                logger.info("CAPTCHA appears to be solved")
                return True
        
        logger.error("CAPTCHA not solved within timeout")
        return False
    
    async def check_rate_limiting(self) -> bool:
        """Check if we're being rate limited"""
        rate_limit_indicators = [
            ':has-text("rate limit")',
            ':has-text("too many requests")',
            ':has-text("slow down")',
            ':has-text("try again later")',
            '.rate-limit',
            '.error-429'
        ]
        
        for indicator in rate_limit_indicators:
            try:
                element = await self.page.wait_for_selector(indicator, timeout=1000)
                if element:
                    logger.warning(f"Rate limiting detected: {indicator}")
                    return True
            except:
                continue
        
        return False
    
    async def handle_rate_limiting(self):
        """Handle rate limiting by waiting"""
        if await self.check_rate_limiting():
            logger.info("Rate limiting detected, waiting before retry...")
            # Wait between 30 seconds to 2 minutes
            wait_time = random.randint(30, 120)
            await asyncio.sleep(wait_time)
    
    async def scroll_randomly(self):
        """Perform random scrolling to appear human"""
        try:
            # Random scroll amount
            scroll_amount = random.randint(100, 500)
            direction = random.choice(['up', 'down'])
            
            if direction == 'down':
                await self.page.mouse.wheel(0, scroll_amount)
            else:
                await self.page.mouse.wheel(0, -scroll_amount)
            
            await self.human_like_delay(500, 1500)
            
        except Exception as e:
            logger.error(f"Error with scrolling: {e}")
    
    async def check_blocked(self) -> bool:
        """Check if we're blocked or detected"""
        blocked_indicators = [
            ':has-text("access denied")',
            ':has-text("blocked")',
            ':has-text("forbidden")',
            ':has-text("bot detected")',
            ':has-text("automated traffic")',
            '.blocked',
            '.access-denied'
        ]
        
        for indicator in blocked_indicators:
            try:
                element = await self.page.wait_for_selector(indicator, timeout=1000)
                if element:
                    logger.error(f"Blocked/detected: {indicator}")
                    return True
            except:
                continue
        
        # Check HTTP status
        try:
            response = await self.page.wait_for_response(lambda r: r.status >= 400, timeout=1000)
            if response and response.status in [403, 429, 503]:
                logger.error(f"HTTP error indicating blocking: {response.status}")
                return True
        except:
            pass
        
        return False
