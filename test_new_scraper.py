"""
Test script to validate the new Selenium-Driverless implementation
"""
import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from loguru import logger
from src.scraper.minecraft_scraper import MinecraftScraper
from src.database.models import TicketData, TicketStatus
from datetime import datetime

async def test_new_scraper():
    """Test the new Selenium-Driverless scraper implementation"""
    logger.info("Starting comprehensive test of new Selenium-Driverless scraper")
    
    # Create test ticket data
    test_ticket = TicketData(
        ticket_id="TEST001",
        user_id=12345,
        ticket_type="Account Issue",
        subject="Test Ticket - Selenium-Driverless Implementation",
        description="This is a test ticket to validate the new Selenium-Driverless implementation. Please ignore this test submission.",
        email="<EMAIL>",
        minecraft_username="TestUser123",
        status=TicketStatus.PENDING,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    try:
        # Test the scraper
        async with <PERSON><PERSON><PERSON><PERSON>rap<PERSON>() as scraper:
            logger.info("Scraper initialized successfully")
            
            # Test navigation
            logger.info("Testing navigation to ticket form...")
            await scraper.navigate_to_ticket_form()
            logger.info("✅ Navigation successful")
            
            # Test form verification
            logger.info("Testing form verification...")
            form_verified = await scraper.verify_ticket_form()
            if form_verified:
                logger.info("✅ Form verification successful")
            else:
                logger.warning("⚠️ Form verification failed")
            
            # Test form filling (without actually submitting)
            logger.info("Testing form filling...")
            form_filled = await scraper.fill_support_form(test_ticket)
            if form_filled:
                logger.info("✅ Form filling successful")
            else:
                logger.error("❌ Form filling failed")
                return False
            
            # Note: We won't actually submit the form to avoid creating test tickets
            logger.info("⚠️ Skipping actual form submission to avoid creating test tickets")
            
            logger.info("🎉 All tests completed successfully!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False

async def test_basic_navigation():
    """Test basic navigation without form filling"""
    logger.info("Testing basic navigation capabilities")
    
    try:
        async with MinecraftScraper() as scraper:
            logger.info("Testing navigation to Minecraft support form...")
            await scraper.navigate_to_ticket_form()
            
            # Verify we can access the page
            current_url = await scraper.target.current_url
            title = await scraper.target.title
            
            logger.info(f"Current URL: {current_url}")
            logger.info(f"Page title: {title}")
            
            # Check if we're on the right page
            if "help.minecraft.net" in current_url and ("request" in current_url.lower() or "contact" in title.lower()):
                logger.info("✅ Successfully navigated to Minecraft support page")
                return True
            else:
                logger.warning("⚠️ May not be on the correct support page")
                return False
                
    except Exception as e:
        logger.error(f"❌ Navigation test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("=" * 60)
    logger.info("TESTING NEW SELENIUM-DRIVERLESS IMPLEMENTATION")
    logger.info("=" * 60)
    
    # Test 1: Basic navigation
    logger.info("\n🔍 Test 1: Basic Navigation")
    nav_success = await test_basic_navigation()
    
    # Test 2: Comprehensive scraper test
    logger.info("\n🔍 Test 2: Comprehensive Scraper Test")
    scraper_success = await test_new_scraper()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Basic Navigation: {'✅ PASS' if nav_success else '❌ FAIL'}")
    logger.info(f"Comprehensive Test: {'✅ PASS' if scraper_success else '❌ FAIL'}")
    
    overall_success = nav_success and scraper_success
    
    if overall_success:
        logger.info("\n🎉 ALL TESTS PASSED! The new Selenium-Driverless implementation is working correctly.")
        logger.info("✅ The scraper can successfully:")
        logger.info("   - Navigate to the Minecraft support form")
        logger.info("   - Verify form elements are present")
        logger.info("   - Fill form fields with human-like behavior")
        logger.info("   - Avoid HTTP/2 protocol errors")
    else:
        logger.error("\n💥 SOME TESTS FAILED! Please review the implementation.")
    
    return overall_success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
